{"gmail": {"credentials_file": "credentials.json", "token_file": "token.json", "scopes": ["https://www.googleapis.com/auth/gmail.readonly", "https://www.googleapis.com/auth/gmail.compose"]}, "ai": {"provider": "openai", "api_key": "your-openai-api-key-here", "model": "gpt-3.5-turbo", "max_tokens": 500, "temperature": 0.7, "alternative_provider": {"provider": "gemini", "api_key": "your-gemini-api-key-here", "model": "gemini-pro"}}, "email": {"target_email": "<EMAIL>", "max_messages_per_thread": 50, "search_query_limit": 100}, "logging": {"level": "INFO", "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "file": "email_reply.log"}, "reply_settings": {"tone_analysis": true, "context_window": 10, "fallback_to_clarification": true, "include_signature": false, "draft_subject_prefix": "Re: "}}