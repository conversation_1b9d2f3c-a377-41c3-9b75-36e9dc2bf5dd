"""
AI-Powered Email Reply Generation Module

This module analyzes email conversations and generates intelligent replies:
- Analyzes conversation tone and context
- Detects common intents (questions, confirmations, follow-ups)
- Generates contextually appropriate replies
- Supports multiple AI providers (OpenAI, Gemini)
- Handles fallback scenarios with polite clarification requests
"""

import logging
import re
from typing import List, Dict, Optional, Tuple
from datetime import datetime
from enum import Enum

import openai
try:
    import google.generativeai as genai
    GEMINI_AVAILABLE = True
except ImportError:
    GEMINI_AVAILABLE = False

from gmail_api import EmailMessage

logger = logging.getLogger(__name__)


class ConversationTone(Enum):
    """Detected conversation tone types."""
    FORMAL = "formal"
    CASUAL = "casual"
    PROFESSIONAL = "professional"
    FRIENDLY = "friendly"
    URGENT = "urgent"
    NEUTRAL = "neutral"


class MessageIntent(Enum):
    """Detected message intent types."""
    QUESTION = "question"
    REQUEST = "request"
    CONFIRMATION = "confirmation"
    INFORMATION = "information"
    FOLLOW_UP = "follow_up"
    GREETING = "greeting"
    CLOSING = "closing"
    UNCLEAR = "unclear"


class ConversationAnalyzer:
    """Analyzes email conversation context and tone."""
    
    def __init__(self):
        # Tone detection patterns
        self.formal_indicators = [
            r'\b(dear|sincerely|regards|respectfully|cordially)\b',
            r'\b(please find|kindly|would you|could you)\b',
            r'\b(thank you for your|i appreciate|grateful)\b'
        ]
        
        self.casual_indicators = [
            r'\b(hey|hi|hello|thanks|thx|btw|fyi)\b',
            r'\b(gonna|wanna|gotta|yeah|yep|nope)\b',
            r'[!]{2,}|[?]{2,}'  # Multiple punctuation
        ]
        
        self.urgent_indicators = [
            r'\b(urgent|asap|immediately|emergency|critical)\b',
            r'\b(deadline|time.sensitive|rush|priority)\b',
            r'[!]{3,}'  # Multiple exclamation marks
        ]
        
        # Intent detection patterns
        self.question_indicators = [
            r'\?',  # Question marks
            r'\b(what|when|where|why|how|who|which|can you|could you|would you|will you)\b',
            r'\b(do you|did you|have you|are you|is it|does it)\b'
        ]
        
        self.request_indicators = [
            r'\b(please|could you|would you|can you|need you to)\b',
            r'\b(send|provide|share|give|help|assist)\b'
        ]
    
    def analyze_tone(self, messages: List[EmailMessage]) -> ConversationTone:
        """
        Analyze the overall tone of the conversation.
        
        Args:
            messages: List of email messages in the thread
            
        Returns:
            Detected conversation tone
        """
        if not messages:
            return ConversationTone.NEUTRAL
        
        # Combine all message text
        all_text = ' '.join([msg.get_clean_text().lower() for msg in messages])
        
        # Count tone indicators
        formal_count = sum(len(re.findall(pattern, all_text, re.IGNORECASE)) 
                          for pattern in self.formal_indicators)
        casual_count = sum(len(re.findall(pattern, all_text, re.IGNORECASE)) 
                          for pattern in self.casual_indicators)
        urgent_count = sum(len(re.findall(pattern, all_text, re.IGNORECASE)) 
                          for pattern in self.urgent_indicators)
        
        # Determine tone based on counts
        if urgent_count > 0:
            return ConversationTone.URGENT
        elif formal_count > casual_count:
            return ConversationTone.FORMAL
        elif casual_count > formal_count:
            return ConversationTone.CASUAL
        else:
            return ConversationTone.PROFESSIONAL
    
    def detect_intent(self, message: EmailMessage) -> MessageIntent:
        """
        Detect the intent of the most recent message.
        
        Args:
            message: Email message to analyze
            
        Returns:
            Detected message intent
        """
        text = message.get_clean_text().lower()
        
        # Check for questions
        question_count = sum(len(re.findall(pattern, text, re.IGNORECASE)) 
                           for pattern in self.question_indicators)
        
        # Check for requests
        request_count = sum(len(re.findall(pattern, text, re.IGNORECASE)) 
                          for pattern in self.request_indicators)
        
        # Determine intent
        if question_count > 0:
            return MessageIntent.QUESTION
        elif request_count > 0:
            return MessageIntent.REQUEST
        elif any(word in text for word in ['confirm', 'confirmation', 'correct', 'right']):
            return MessageIntent.CONFIRMATION
        elif any(word in text for word in ['update', 'status', 'progress', 'follow']):
            return MessageIntent.FOLLOW_UP
        elif len(text.split()) < 10:  # Short messages might be greetings/closings
            if any(word in text for word in ['hi', 'hello', 'hey', 'good morning', 'good afternoon']):
                return MessageIntent.GREETING
            elif any(word in text for word in ['thanks', 'thank you', 'regards', 'best']):
                return MessageIntent.CLOSING
        
        return MessageIntent.INFORMATION


class AIReplyGenerator:
    """Generates AI-powered email replies."""
    
    def __init__(self, config: dict):
        self.config = config
        self.ai_config = config.get('ai', {})
        self.provider = self.ai_config.get('provider', 'openai').lower()
        
        # Initialize AI client
        if self.provider == 'openai':
            openai.api_key = self.ai_config.get('api_key')
        elif self.provider == 'gemini' and GEMINI_AVAILABLE:
            genai.configure(api_key=self.ai_config.get('api_key'))
        
        self.analyzer = ConversationAnalyzer()
    
    def generate_reply(self, messages: List[EmailMessage], target_email: str) -> Optional[str]:
        """
        Generate an AI reply based on conversation context.
        
        Args:
            messages: List of email messages in the thread
            target_email: Email address we're replying to
            
        Returns:
            Generated reply text or None if generation fails
        """
        if not messages:
            logger.warning("No messages provided for reply generation")
            return None
        
        try:
            # Analyze conversation
            tone = self.analyzer.analyze_tone(messages)
            latest_message = messages[-1]
            intent = self.analyzer.detect_intent(latest_message)
            
            logger.info(f"Detected tone: {tone.value}, intent: {intent.value}")
            
            # Build context for AI
            context = self._build_conversation_context(messages, target_email)
            
            # Generate reply based on provider
            if self.provider == 'openai':
                return self._generate_openai_reply(context, tone, intent)
            elif self.provider == 'gemini' and GEMINI_AVAILABLE:
                return self._generate_gemini_reply(context, tone, intent)
            else:
                logger.error(f"Unsupported AI provider: {self.provider}")
                return self._generate_fallback_reply(intent)
                
        except Exception as e:
            logger.error(f"Error generating AI reply: {e}")
            return self._generate_fallback_reply(intent)
    
    def _build_conversation_context(self, messages: List[EmailMessage], target_email: str) -> str:
        """Build conversation context for AI prompt."""
        context_lines = []
        context_lines.append("Email Conversation History:")
        context_lines.append("=" * 40)
        
        # Limit context to recent messages
        max_context = self.config.get('reply_settings', {}).get('context_window', 10)
        recent_messages = messages[-max_context:] if len(messages) > max_context else messages
        
        for i, msg in enumerate(recent_messages):
            sender = "Target" if target_email.lower() in msg.sender.lower() else "Me"
            timestamp = datetime.fromtimestamp(msg.internal_date / 1000).strftime("%Y-%m-%d %H:%M")
            
            context_lines.append(f"\n[{timestamp}] {sender}:")
            context_lines.append(msg.get_clean_text()[:500])  # Limit message length
            
            if i < len(recent_messages) - 1:
                context_lines.append("-" * 20)
        
        return "\n".join(context_lines)
    
    def _generate_openai_reply(self, context: str, tone: ConversationTone, intent: MessageIntent) -> Optional[str]:
        """Generate reply using OpenAI."""
        try:
            # Build prompt based on tone and intent
            system_prompt = self._build_system_prompt(tone, intent)
            
            response = openai.ChatCompletion.create(
                model=self.ai_config.get('model', 'gpt-3.5-turbo'),
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": f"Based on this conversation, generate an appropriate reply:\n\n{context}"}
                ],
                max_tokens=self.ai_config.get('max_tokens', 500),
                temperature=self.ai_config.get('temperature', 0.7)
            )
            
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            logger.error(f"OpenAI API error: {e}")
            return None

    def _generate_gemini_reply(self, context: str, tone: ConversationTone, intent: MessageIntent) -> Optional[str]:
        """Generate reply using Google Gemini."""
        if not GEMINI_AVAILABLE:
            logger.error("Gemini not available")
            return None

        try:
            model = genai.GenerativeModel(self.ai_config.get('model', 'gemini-pro'))

            # Build prompt
            system_prompt = self._build_system_prompt(tone, intent)
            full_prompt = f"{system_prompt}\n\nBased on this conversation, generate an appropriate reply:\n\n{context}"

            response = model.generate_content(full_prompt)
            return response.text.strip()

        except Exception as e:
            logger.error(f"Gemini API error: {e}")
            return None

    def _build_system_prompt(self, tone: ConversationTone, intent: MessageIntent) -> str:
        """Build system prompt based on detected tone and intent."""
        base_prompt = """You are an AI assistant helping to write email replies.
Generate a professional, contextually appropriate response based on the conversation history.

Guidelines:
- Keep the reply concise and relevant
- Match the tone and style of the conversation
- Address the main points from the latest message
- Be helpful and professional
- Do not include email signatures or headers
"""

        # Tone-specific instructions
        tone_instructions = {
            ConversationTone.FORMAL: "Use formal language, proper grammar, and professional tone.",
            ConversationTone.CASUAL: "Use casual, friendly language that matches the informal tone.",
            ConversationTone.PROFESSIONAL: "Maintain a professional but approachable tone.",
            ConversationTone.FRIENDLY: "Be warm and friendly while remaining professional.",
            ConversationTone.URGENT: "Acknowledge the urgency and provide a prompt, clear response.",
            ConversationTone.NEUTRAL: "Use a balanced, professional tone."
        }

        # Intent-specific instructions
        intent_instructions = {
            MessageIntent.QUESTION: "Answer the questions clearly and completely.",
            MessageIntent.REQUEST: "Address the request and provide helpful information or next steps.",
            MessageIntent.CONFIRMATION: "Provide confirmation or clarification as needed.",
            MessageIntent.INFORMATION: "Acknowledge the information and respond appropriately.",
            MessageIntent.FOLLOW_UP: "Provide an update or status as requested.",
            MessageIntent.GREETING: "Respond with an appropriate greeting.",
            MessageIntent.CLOSING: "Provide a suitable closing response.",
            MessageIntent.UNCLEAR: "Ask for clarification in a polite, helpful manner."
        }

        # Combine instructions
        full_prompt = f"{base_prompt}\n\nTone: {tone_instructions.get(tone, '')}\nIntent: {intent_instructions.get(intent, '')}"

        return full_prompt

    def _generate_fallback_reply(self, intent: MessageIntent) -> str:
        """Generate a fallback reply when AI generation fails."""
        fallback_messages = {
            MessageIntent.QUESTION: "Thank you for your email. I'd be happy to help answer your questions. Could you please provide a bit more detail so I can give you the most accurate response?",
            MessageIntent.REQUEST: "Thank you for reaching out. I've received your request and will get back to you with the information you need as soon as possible.",
            MessageIntent.CONFIRMATION: "Thank you for the confirmation. I've noted this information.",
            MessageIntent.INFORMATION: "Thank you for the information. I appreciate you keeping me updated.",
            MessageIntent.FOLLOW_UP: "Thank you for following up. I'll review the current status and get back to you with an update shortly.",
            MessageIntent.GREETING: "Thank you for your email. I hope you're doing well.",
            MessageIntent.CLOSING: "Thank you for your message. Please don't hesitate to reach out if you need anything else.",
            MessageIntent.UNCLEAR: "Thank you for your email. Could you please provide a bit more detail about what you're looking for? I'd be happy to help once I better understand your needs."
        }

        return fallback_messages.get(intent,
            "Thank you for your email. I've received your message and will respond appropriately. "
            "If you need immediate assistance, please let me know.")
