"""
Gmail OAuth2 Authentication Module

This module handles OAuth2 authentication with Gmail API, including:
- Initial authentication flow
- Token refresh and management
- Secure credential storage
- Error handling for authentication failures

Setup Instructions:
1. Go to Google Cloud Console (https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable Gmail API in "APIs & Services" > "Library"
4. Create OAuth2 credentials in "APIs & Services" > "Credentials"
5. Choose "Desktop application" and download JSON file as 'credentials.json'
"""

import os
import json
import logging
from typing import Optional, List
from pathlib import Path

from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

logger = logging.getLogger(__name__)


class GmailAuthenticator:
    """Handles Gmail OAuth2 authentication and API service creation."""
    
    def __init__(self, credentials_file: str, token_file: str, scopes: List[str]):
        """
        Initialize the authenticator.
        
        Args:
            credentials_file: Path to OAuth2 credentials JSON file
            token_file: Path to store/load access tokens
            scopes: List of Gmail API scopes to request
        """
        self.credentials_file = Path(credentials_file)
        self.token_file = Path(token_file)
        self.scopes = scopes
        self.credentials: Optional[Credentials] = None
        self.service = None
        
    def authenticate(self) -> bool:
        """
        Perform OAuth2 authentication flow.
        
        Returns:
            bool: True if authentication successful, False otherwise
        """
        try:
            logger.info("Starting Gmail authentication...")
            
            # Check if credentials file exists
            if not self.credentials_file.exists():
                logger.error(f"Credentials file not found: {self.credentials_file}")
                logger.error("Please download credentials.json from Google Cloud Console")
                return False
            
            # Load existing token if available
            if self.token_file.exists():
                logger.info("Loading existing token...")
                self.credentials = Credentials.from_authorized_user_file(
                    str(self.token_file), self.scopes
                )
            
            # If no valid credentials, run OAuth flow
            if not self.credentials or not self.credentials.valid:
                if self.credentials and self.credentials.expired and self.credentials.refresh_token:
                    logger.info("Refreshing expired token...")
                    self.credentials.refresh(Request())
                else:
                    logger.info("Starting OAuth2 flow...")
                    logger.info("A browser window will open for authentication")
                    
                    flow = InstalledAppFlow.from_client_secrets_file(
                        str(self.credentials_file), self.scopes
                    )
                    self.credentials = flow.run_local_server(port=0)
                
                # Save credentials for future use
                logger.info("Saving authentication token...")
                with open(self.token_file, 'w') as token:
                    token.write(self.credentials.to_json())
            
            # Build Gmail service
            logger.info("Building Gmail API service...")
            self.service = build('gmail', 'v1', credentials=self.credentials)
            
            # Test the connection
            profile = self.service.users().getProfile(userId='me').execute()
            logger.info(f"Successfully authenticated as: {profile.get('emailAddress')}")
            
            return True
            
        except FileNotFoundError as e:
            logger.error(f"File not found: {e}")
            return False
        except HttpError as e:
            logger.error(f"Gmail API error: {e}")
            return False
        except Exception as e:
            logger.error(f"Authentication failed: {e}")
            return False
    
    def get_service(self):
        """
        Get the authenticated Gmail service.
        
        Returns:
            Gmail API service object or None if not authenticated
        """
        if not self.service:
            logger.warning("Not authenticated. Call authenticate() first.")
            return None
        return self.service
    
    def is_authenticated(self) -> bool:
        """Check if currently authenticated with valid credentials."""
        return (self.credentials is not None and 
                self.credentials.valid and 
                self.service is not None)
    
    def revoke_credentials(self) -> bool:
        """
        Revoke current credentials and remove token file.
        
        Returns:
            bool: True if successfully revoked, False otherwise
        """
        try:
            if self.credentials:
                # Revoke the credentials
                self.credentials.revoke(Request())
                logger.info("Credentials revoked successfully")
            
            # Remove token file
            if self.token_file.exists():
                self.token_file.unlink()
                logger.info("Token file removed")
            
            # Reset instance variables
            self.credentials = None
            self.service = None
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to revoke credentials: {e}")
            return False


def create_authenticator(config: dict) -> GmailAuthenticator:
    """
    Create a GmailAuthenticator from configuration.
    
    Args:
        config: Configuration dictionary with gmail settings
        
    Returns:
        GmailAuthenticator instance
    """
    gmail_config = config.get('gmail', {})
    
    return GmailAuthenticator(
        credentials_file=gmail_config.get('credentials_file', 'credentials.json'),
        token_file=gmail_config.get('token_file', 'token.json'),
        scopes=gmail_config.get('scopes', [
            'https://www.googleapis.com/auth/gmail.readonly',
            'https://www.googleapis.com/auth/gmail.compose'
        ])
    )
