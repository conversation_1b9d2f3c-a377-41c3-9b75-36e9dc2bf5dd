#!/usr/bin/env python3
"""
Gmail AI Reply Generator - Main Application

This is the main entry point for the Gmail AI Reply Generator.
It orchestrates the entire process:
1. Load configuration
2. Authenticate with Gmail
3. Search for email threads
4. Analyze conversations
5. Generate AI replies
6. Create drafts in Gmail

Usage:
    python main.py [--config config.json] [--email <EMAIL>]
"""

import json
import logging
import sys
import argparse
from pathlib import Path
from typing import Optional, Dict, List
from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.table import Table
from rich.panel import Panel

from auth import create_authenticator
from gmail_api import GmailAPI, EmailMessage
from ai_reply import AIReplyGenerator

# Setup rich console for beautiful output
console = Console()

def setup_logging(config: Dict) -> None:
    """Setup logging configuration."""
    log_config = config.get('logging', {})
    
    logging.basicConfig(
        level=getattr(logging, log_config.get('level', 'INFO')),
        format=log_config.get('format', '%(asctime)s - %(name)s - %(levelname)s - %(message)s'),
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler(log_config.get('file', 'email_reply.log'))
        ]
    )

def load_config(config_path: str) -> Optional[Dict]:
    """Load configuration from JSON file."""
    try:
        with open(config_path, 'r') as f:
            config = json.load(f)
        console.print(f"✅ Configuration loaded from {config_path}")
        return config
    except FileNotFoundError:
        console.print(f"❌ Configuration file not found: {config_path}")
        console.print("Please copy config.example.json to config.json and update it with your settings.")
        return None
    except json.JSONDecodeError as e:
        console.print(f"❌ Invalid JSON in configuration file: {e}")
        return None

def validate_config(config: Dict) -> bool:
    """Validate configuration has required fields."""
    required_fields = [
        ('gmail', 'credentials_file'),
        ('ai', 'api_key'),
        ('email', 'target_email')
    ]
    
    for section, field in required_fields:
        if section not in config or field not in config[section]:
            console.print(f"❌ Missing required configuration: {section}.{field}")
            return False
        
        if not config[section][field]:
            console.print(f"❌ Empty required configuration: {section}.{field}")
            return False
    
    return True

def display_thread_summary(messages: List[EmailMessage], target_email: str) -> None:
    """Display a summary of the email thread."""
    if not messages:
        return
    
    table = Table(title="Email Thread Summary")
    table.add_column("Date", style="cyan")
    table.add_column("From", style="green")
    table.add_column("Subject", style="yellow")
    table.add_column("Preview", style="white")
    
    for msg in messages[-5:]:  # Show last 5 messages
        sender = "Target" if target_email.lower() in msg.sender.lower() else "You"
        preview = msg.get_clean_text()[:50] + "..." if len(msg.get_clean_text()) > 50 else msg.get_clean_text()
        
        table.add_row(
            msg.date[:10] if msg.date else "Unknown",
            sender,
            msg.subject[:30] + "..." if len(msg.subject) > 30 else msg.subject,
            preview
        )
    
    console.print(table)

def process_email_thread(gmail_api: GmailAPI, ai_generator: AIReplyGenerator, 
                        thread_id: str, target_email: str, config: Dict) -> bool:
    """Process a single email thread and generate a reply."""
    try:
        # Get thread messages
        console.print(f"📧 Analyzing thread {thread_id[:8]}...")
        messages = gmail_api.get_thread_messages(
            thread_id, 
            config.get('email', {}).get('max_messages_per_thread', 50)
        )
        
        if not messages:
            console.print("❌ No messages found in thread")
            return False
        
        # Display thread summary
        display_thread_summary(messages, target_email)
        
        # Check if the last message is from the target email
        last_message = messages[-1]
        if not last_message.is_from_sender(target_email):
            console.print("ℹ️  Last message is not from target email, skipping...")
            return False
        
        # Generate AI reply
        console.print("🤖 Generating AI reply...")
        reply_text = ai_generator.generate_reply(messages, target_email)
        
        if not reply_text:
            console.print("❌ Failed to generate reply")
            return False
        
        # Display generated reply
        console.print(Panel(reply_text, title="Generated Reply", border_style="green"))
        
        # Ask user for confirmation
        if not console.input("Create draft? [Y/n]: ").lower() in ['', 'y', 'yes']:
            console.print("⏭️  Skipping draft creation")
            return False
        
        # Create draft
        console.print("📝 Creating draft...")
        draft_id = gmail_api.create_draft_reply(
            thread_id=thread_id,
            reply_text=reply_text,
            original_subject=last_message.subject,
            to_email=target_email
        )
        
        if draft_id:
            console.print(f"✅ Draft created successfully! Draft ID: {draft_id}")
            console.print(f"📧 Subject: {last_message.subject}")
            return True
        else:
            console.print("❌ Failed to create draft")
            return False
            
    except Exception as e:
        console.print(f"❌ Error processing thread: {e}")
        return False

def main():
    """Main application entry point."""
    parser = argparse.ArgumentParser(description="Gmail AI Reply Generator")
    parser.add_argument('--config', default='config.json', help='Configuration file path')
    parser.add_argument('--email', help='Target email address (overrides config)')
    parser.add_argument('--max-threads', type=int, default=5, help='Maximum threads to process')
    
    args = parser.parse_args()
    
    # Display banner
    console.print(Panel.fit("📧 Gmail AI Reply Generator", style="bold blue"))
    
    # Load configuration
    config = load_config(args.config)
    if not config:
        sys.exit(1)
    
    # Override target email if provided
    if args.email:
        config['email']['target_email'] = args.email
    
    # Validate configuration
    if not validate_config(config):
        sys.exit(1)
    
    # Setup logging
    setup_logging(config)
    logger = logging.getLogger(__name__)
    
    target_email = config['email']['target_email']
    console.print(f"🎯 Target email: {target_email}")
    
    try:
        # Authenticate with Gmail
        console.print("🔐 Authenticating with Gmail...")
        authenticator = create_authenticator(config)
        
        if not authenticator.authenticate():
            console.print("❌ Authentication failed")
            sys.exit(1)
        
        gmail_service = authenticator.get_service()
        gmail_api = GmailAPI(gmail_service)
        
        # Initialize AI reply generator
        console.print("🤖 Initializing AI reply generator...")
        ai_generator = AIReplyGenerator(config)
        
        # Search for threads
        console.print(f"🔍 Searching for threads with {target_email}...")
        thread_ids = gmail_api.search_threads_with_email(
            target_email, 
            config.get('email', {}).get('search_query_limit', 100)
        )
        
        if not thread_ids:
            console.print("❌ No threads found")
            sys.exit(0)
        
        console.print(f"📊 Found {len(thread_ids)} threads")
        
        # Process threads
        processed_count = 0
        success_count = 0
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            task = progress.add_task("Processing threads...", total=min(len(thread_ids), args.max_threads))
            
            for i, thread_id in enumerate(thread_ids[:args.max_threads]):
                progress.update(task, description=f"Processing thread {i+1}/{min(len(thread_ids), args.max_threads)}")
                
                if process_email_thread(gmail_api, ai_generator, thread_id, target_email, config):
                    success_count += 1
                
                processed_count += 1
                progress.advance(task)
        
        # Summary
        console.print(f"\n📊 Summary:")
        console.print(f"   Threads processed: {processed_count}")
        console.print(f"   Drafts created: {success_count}")
        console.print(f"   Success rate: {success_count/processed_count*100:.1f}%")
        
        console.print("\n✅ Process completed!")
        
    except KeyboardInterrupt:
        console.print("\n⏹️  Process interrupted by user")
        sys.exit(0)
    except Exception as e:
        console.print(f"\n❌ Unexpected error: {e}")
        logger.exception("Unexpected error in main")
        sys.exit(1)

if __name__ == "__main__":
    main()
