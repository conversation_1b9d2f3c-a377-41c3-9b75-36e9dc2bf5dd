# Gmail AI Reply Generator

A production-ready Python application that uses OAuth2 to authenticate with Gmail, analyzes email conversations, and generates intelligent replies using AI models.

## Features

- 🔐 **Secure OAuth2 Authentication** with Gmail API
- 📧 **Thread Analysis** - Analyzes full conversation history
- 🤖 **AI-Powered Replies** - Uses OpenAI GPT or Google Gemini
- 📝 **Draft Creation** - Saves replies as Gmail drafts (no auto-send)
- 🎯 **Tone Matching** - Adapts to conversation style
- 🛡️ **Error Handling** - Robust error handling and logging

## Quick Start

### 1. Google Cloud Setup

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable the Gmail API:
   - Navigate to "APIs & Services" > "Library"
   - Search for "Gmail API" and enable it
4. Create OAuth2 credentials:
   - Go to "APIs & Services" > "Credentials"
   - Click "Create Credentials" > "OAuth 2.0 Client IDs"
   - Choose "Desktop application"
   - Download the JSON file and save as `credentials.json`

### 2. Installation

```bash
# Clone or download the project
cd emailreply

# Install dependencies
pip install -r requirements.txt

# Copy and configure settings
cp config.example.json config.json
# Edit config.json with your settings
```

### 3. Configuration

Edit `config.json`:
- Add your OpenAI API key or Gemini API key
- Set the target email address to analyze
- Adjust AI model settings as needed

### 4. First Run

```bash
python main.py
```

On first run, you'll be prompted to authenticate with Google OAuth2.

## Configuration Options

See `config.example.json` for all available settings:

- **Gmail Settings**: OAuth credentials and API scopes
- **AI Settings**: Model selection, temperature, token limits
- **Email Settings**: Target email, search limits
- **Reply Settings**: Tone analysis, context window, fallback behavior

## Usage

The script will:
1. Authenticate with Gmail using OAuth2
2. Search for email threads with the specified email address
3. Analyze conversation context and tone
4. Generate appropriate AI replies
5. Save replies as drafts in Gmail

## Security Notes

- Never commit `credentials.json` or `token.json` to version control
- Store API keys securely (consider environment variables)
- Review generated drafts before sending

## Advanced Usage

### Command Line Options

```bash
# Process specific email address
python main.py --email <EMAIL>

# Use custom config file
python main.py --config my_config.json

# Limit number of threads to process
python main.py --max-threads 3

# Combine options
python main.py --email <EMAIL> --max-threads 5
```

### Environment Variables

You can also use environment variables for sensitive data:

```bash
export OPENAI_API_KEY="your-api-key"
export GEMINI_API_KEY="your-gemini-key"
```

Then reference them in your config:
```json
{
  "ai": {
    "api_key": "${OPENAI_API_KEY}"
  }
}
```

## API Provider Setup

### OpenAI Setup

1. Go to [OpenAI API](https://platform.openai.com/api-keys)
2. Create a new API key
3. Add it to your `config.json`:

```json
{
  "ai": {
    "provider": "openai",
    "api_key": "sk-your-key-here",
    "model": "gpt-3.5-turbo",
    "max_tokens": 500,
    "temperature": 0.7
  }
}
```

### Google Gemini Setup

1. Go to [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create a new API key
3. Add it to your `config.json`:

```json
{
  "ai": {
    "provider": "gemini",
    "api_key": "your-gemini-key-here",
    "model": "gemini-pro"
  }
}
```

## Troubleshooting

### Authentication Issues

**Problem**: "Credentials file not found"
- **Solution**: Download `credentials.json` from Google Cloud Console and place it in the project directory

**Problem**: "Authentication failed"
- **Solution**: Delete `token.json` and re-authenticate. Make sure your Google Cloud project has Gmail API enabled.

**Problem**: "Access blocked: This app's request is invalid"
- **Solution**: In Google Cloud Console, add your email to test users in OAuth consent screen settings

### API Issues

**Problem**: "OpenAI API error: Incorrect API key"
- **Solution**: Verify your OpenAI API key in the config file

**Problem**: "Rate limit exceeded"
- **Solution**: The app has built-in rate limiting. Wait a few minutes and try again.

**Problem**: "Gmail API quota exceeded"
- **Solution**: Check your Google Cloud Console quotas and limits

### Configuration Issues

**Problem**: "Missing required configuration"
- **Solution**: Copy `config.example.json` to `config.json` and fill in all required fields

**Problem**: "Invalid JSON in configuration file"
- **Solution**: Validate your JSON syntax using a JSON validator

### Email Processing Issues

**Problem**: "No threads found"
- **Solution**: Verify the target email address is correct and you have email history with that contact

**Problem**: "Failed to generate reply"
- **Solution**: Check your AI API key and internet connection. The app will use fallback replies if AI generation fails.

## File Structure

```
emailreply/
├── main.py              # Main application entry point
├── auth.py              # Gmail OAuth2 authentication
├── gmail_api.py         # Gmail API interactions
├── ai_reply.py          # AI reply generation
├── utils.py             # Utility functions and error handling
├── config.json          # Your configuration (create from example)
├── config.example.json  # Configuration template
├── requirements.txt     # Python dependencies
├── credentials.json     # OAuth2 credentials (download from Google)
├── token.json          # OAuth2 tokens (auto-generated)
├── README.md           # This file
├── .gitignore          # Git ignore rules
└── email_reply.log     # Application logs
```

## Security Best Practices

1. **Never commit sensitive files**:
   - `credentials.json`
   - `token.json`
   - `config.json` (if it contains API keys)

2. **Use environment variables** for API keys in production

3. **Regularly rotate API keys**

4. **Review generated drafts** before sending

5. **Monitor API usage** to avoid unexpected charges

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
