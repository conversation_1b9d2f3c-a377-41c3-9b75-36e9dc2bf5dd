"""
Gmail API Interaction Module

This module provides high-level functions for interacting with Gmail API:
- Search for email threads by participant
- Retrieve full conversation history
- Parse email content and metadata
- Create drafts in Gmail
- Handle API rate limits and errors
"""

import base64
import email
import logging
import time
from typing import List, Dict, Optional, Tuple
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import html2text
from bs4 import BeautifulSoup

from googleapiclient.errors import HttpError

logger = logging.getLogger(__name__)


class EmailMessage:
    """Represents a single email message in a thread."""
    
    def __init__(self, message_data: dict):
        self.id = message_data.get('id')
        self.thread_id = message_data.get('threadId')
        self.snippet = message_data.get('snippet', '')
        self.payload = message_data.get('payload', {})
        self.internal_date = int(message_data.get('internalDate', 0))
        
        # Parse headers
        self.headers = {}
        for header in self.payload.get('headers', []):
            self.headers[header['name'].lower()] = header['value']
        
        # Extract key information
        self.sender = self.headers.get('from', '')
        self.recipient = self.headers.get('to', '')
        self.subject = self.headers.get('subject', '')
        self.date = self.headers.get('date', '')
        
        # Extract body content
        self.body_text = self._extract_body_text()
        self.body_html = self._extract_body_html()
    
    def _extract_body_text(self) -> str:
        """Extract plain text body from message."""
        return self._extract_body_by_type('text/plain')
    
    def _extract_body_html(self) -> str:
        """Extract HTML body from message."""
        return self._extract_body_by_type('text/html')
    
    def _extract_body_by_type(self, mime_type: str) -> str:
        """Extract body content by MIME type."""
        def extract_from_part(part):
            if part.get('mimeType') == mime_type:
                body_data = part.get('body', {}).get('data')
                if body_data:
                    return base64.urlsafe_b64decode(body_data).decode('utf-8')
            
            # Check nested parts
            for subpart in part.get('parts', []):
                result = extract_from_part(subpart)
                if result:
                    return result
            return ''
        
        return extract_from_part(self.payload)
    
    def get_clean_text(self) -> str:
        """Get clean text content, preferring plain text over HTML."""
        if self.body_text:
            return self.body_text.strip()
        elif self.body_html:
            # Convert HTML to text
            h = html2text.HTML2Text()
            h.ignore_links = True
            h.ignore_images = True
            return h.handle(self.body_html).strip()
        else:
            return self.snippet
    
    def is_from_sender(self, email_address: str) -> bool:
        """Check if message is from specified email address."""
        return email_address.lower() in self.sender.lower()


class GmailAPI:
    """High-level Gmail API interface."""
    
    def __init__(self, service):
        self.service = service
        self.user_id = 'me'
    
    def search_threads_with_email(self, email_address: str, max_results: int = 100) -> List[str]:
        """
        Search for email threads involving a specific email address.
        
        Args:
            email_address: Email address to search for
            max_results: Maximum number of threads to return
            
        Returns:
            List of thread IDs
        """
        try:
            logger.info(f"Searching for threads with {email_address}...")
            
            # Build search query
            query = f"from:{email_address} OR to:{email_address}"
            
            # Search for threads
            results = self.service.users().threads().list(
                userId=self.user_id,
                q=query,
                maxResults=max_results
            ).execute()
            
            threads = results.get('threads', [])
            thread_ids = [thread['id'] for thread in threads]
            
            logger.info(f"Found {len(thread_ids)} threads")
            return thread_ids
            
        except HttpError as e:
            logger.error(f"Error searching threads: {e}")
            return []
    
    def get_thread_messages(self, thread_id: str, max_messages: int = 50) -> List[EmailMessage]:
        """
        Get all messages in a thread.
        
        Args:
            thread_id: Gmail thread ID
            max_messages: Maximum number of messages to retrieve
            
        Returns:
            List of EmailMessage objects, sorted by date (oldest first)
        """
        try:
            logger.debug(f"Retrieving thread {thread_id}...")
            
            # Get thread with all messages
            thread = self.service.users().threads().get(
                userId=self.user_id,
                id=thread_id,
                format='full'
            ).execute()
            
            messages = thread.get('messages', [])
            
            # Limit number of messages
            if len(messages) > max_messages:
                logger.warning(f"Thread has {len(messages)} messages, limiting to {max_messages}")
                messages = messages[-max_messages:]  # Get most recent messages
            
            # Convert to EmailMessage objects
            email_messages = []
            for msg_data in messages:
                try:
                    email_msg = EmailMessage(msg_data)
                    email_messages.append(email_msg)
                except Exception as e:
                    logger.warning(f"Failed to parse message {msg_data.get('id')}: {e}")
            
            # Sort by internal date (oldest first)
            email_messages.sort(key=lambda x: x.internal_date)
            
            logger.debug(f"Retrieved {len(email_messages)} messages from thread")
            return email_messages
            
        except HttpError as e:
            logger.error(f"Error retrieving thread {thread_id}: {e}")
            return []
    
    def create_draft_reply(self, thread_id: str, reply_text: str, 
                          original_subject: str = "", to_email: str = "") -> Optional[str]:
        """
        Create a draft reply in Gmail.
        
        Args:
            thread_id: Thread ID to reply to
            reply_text: Reply message content
            original_subject: Original email subject
            to_email: Recipient email address
            
        Returns:
            Draft ID if successful, None otherwise
        """
        try:
            logger.info("Creating draft reply...")
            
            # Create the message
            message = MIMEText(reply_text)
            message['to'] = to_email
            
            # Set subject with Re: prefix if not already present
            subject = original_subject
            if subject and not subject.lower().startswith('re:'):
                subject = f"Re: {subject}"
            message['subject'] = subject
            
            # Encode message
            raw_message = base64.urlsafe_b64encode(message.as_bytes()).decode('utf-8')
            
            # Create draft
            draft_body = {
                'message': {
                    'raw': raw_message,
                    'threadId': thread_id
                }
            }
            
            draft = self.service.users().drafts().create(
                userId=self.user_id,
                body=draft_body
            ).execute()
            
            draft_id = draft.get('id')
            logger.info(f"Draft created successfully with ID: {draft_id}")
            
            return draft_id
            
        except HttpError as e:
            logger.error(f"Error creating draft: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error creating draft: {e}")
            return None
    
    def get_user_email(self) -> Optional[str]:
        """Get the authenticated user's email address."""
        try:
            profile = self.service.users().getProfile(userId=self.user_id).execute()
            return profile.get('emailAddress')
        except HttpError as e:
            logger.error(f"Error getting user profile: {e}")
            return None
